#!/usr/bin/env python3
# 简单测试注释删除功能

from comment_remover import CommentRemover

def test_basic_functionality():
    remover = CommentRemover()
    
    # 读取测试文件
    with open('test_sample.c', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("原始内容:")
    print(content)
    print("\n" + "="*50 + "\n")
    
    # 删除注释
    cleaned = remover.remove_comments(content)
    
    print("删除注释后:")
    print(cleaned)
    
    # 保存结果
    with open('test_sample_cleaned.c', 'w', encoding='utf-8') as f:
        f.write(cleaned)
    
    print("\n处理完成，结果保存到 test_sample_cleaned.c")

if __name__ == "__main__":
    test_basic_functionality()
