#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C/C++注释删除工具
使用状态机算法安全删除注释，保护字符串内容
"""

import os
import re
from enum import Enum
from typing import List, Tuple

class State(Enum):
    NORMAL = 1          # 正常代码状态
    IN_STRING = 2       # 在字符串内
    IN_CHAR = 3         # 在字符常量内
    IN_SINGLE_COMMENT = 4  # 在单行注释内
    IN_MULTI_COMMENT = 5   # 在多行注释内

class CommentRemover:
    def __init__(self):
        self.state = State.NORMAL
        self.result = []
        self.i = 0
        self.content = ""
        
    def remove_comments(self, content: str) -> str:
        """删除C/C++注释的主函数"""
        self.content = content
        self.result = []
        self.i = 0
        self.state = State.NORMAL
        
        while self.i < len(content):
            char = content[self.i]
            
            if self.state == State.NORMAL:
                self._handle_normal_state(char)
            elif self.state == State.IN_STRING:
                self._handle_string_state(char)
            elif self.state == State.IN_CHAR:
                self._handle_char_state(char)
            elif self.state == State.IN_SINGLE_COMMENT:
                self._handle_single_comment_state(char)
            elif self.state == State.IN_MULTI_COMMENT:
                self._handle_multi_comment_state(char)
                
            self.i += 1
            
        return ''.join(self.result)
    
    def _handle_normal_state(self, char: str):
        """处理正常代码状态"""
        if char == '"':
            self.state = State.IN_STRING
            self.result.append(char)
        elif char == "'":
            self.state = State.IN_CHAR
            self.result.append(char)
        elif char == '/' and self._peek() == '/':
            self.state = State.IN_SINGLE_COMMENT
            self.i += 1  # 跳过第二个/
        elif char == '/' and self._peek() == '*':
            self.state = State.IN_MULTI_COMMENT
            self.i += 1  # 跳过*
        else:
            self.result.append(char)
    
    def _handle_string_state(self, char: str):
        """处理字符串状态"""
        self.result.append(char)
        if char == '"' and not self._is_escaped():
            self.state = State.NORMAL
    
    def _handle_char_state(self, char: str):
        """处理字符常量状态"""
        self.result.append(char)
        if char == "'" and not self._is_escaped():
            self.state = State.NORMAL
    
    def _handle_single_comment_state(self, char: str):
        """处理单行注释状态"""
        if char == '\n':
            self.state = State.NORMAL
            self.result.append(char)  # 保留换行符
    
    def _handle_multi_comment_state(self, char: str):
        """处理多行注释状态"""
        if char == '*' and self._peek() == '/':
            self.state = State.NORMAL
            self.i += 1  # 跳过/
        elif char == '\n':
            self.result.append(char)  # 保留换行符维持格式
    
    def _peek(self) -> str:
        """查看下一个字符"""
        if self.i + 1 < len(self.content):
            return self.content[self.i + 1]
        return ''
    
    def _is_escaped(self) -> bool:
        """检查当前字符是否被转义"""
        if self.i == 0:
            return False
        
        escape_count = 0
        j = self.i - 1
        while j >= 0 and self.content[j] == '\\':
            escape_count += 1
            j -= 1
        
        return escape_count % 2 == 1

def process_file(file_path: str) -> bool:
    """处理单个文件"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        remover = CommentRemover()
        cleaned_content = remover.remove_comments(content)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        print(f"已处理: {file_path}")
        return True
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False

def process_directory(directory: str, extensions: List[str] = ['.c', '.h']) -> Tuple[int, int]:
    """处理目录下的所有指定扩展名文件"""
    processed = 0
    failed = 0
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                file_path = os.path.join(root, file)
                if process_file(file_path):
                    processed += 1
                else:
                    failed += 1
    
    return processed, failed

def test_comment_remover():
    """测试注释删除功能"""
    test_cases = [
        # 测试单行注释
        ('int a = 5; // 这是注释\nint b = 6;', 'int a = 5; \nint b = 6;'),
        
        # 测试多行注释
        ('int a = 5; /* 这是\n多行注释 */ int b = 6;', 'int a = 5; \n int b = 6;'),
        
        # 测试字符串内的注释符号
        ('printf("// 这不是注释");', 'printf("// 这不是注释");'),
        ('printf("/* 这也不是注释 */");', 'printf("/* 这也不是注释 */");'),
        
        # 测试字符常量
        ("char c = '/'; char d = '*';", "char c = '/'; char d = '*';"),
        
        # 测试转义字符
        ('printf("He said \\"Hello//World\\"");', 'printf("He said \\"Hello//World\\"");'),
    ]
    
    remover = CommentRemover()
    all_passed = True
    
    for i, (input_text, expected) in enumerate(test_cases):
        result = remover.remove_comments(input_text)
        if result == expected:
            print(f"测试 {i+1}: 通过")
        else:
            print(f"测试 {i+1}: 失败")
            print(f"  输入: {repr(input_text)}")
            print(f"  期望: {repr(expected)}")
            print(f"  实际: {repr(result)}")
            all_passed = False
    
    return all_passed

if __name__ == "__main__":
    print("C/C++注释删除工具")
    print("=" * 40)
    
    # 运行测试
    print("运行测试用例...")
    if test_comment_remover():
        print("所有测试通过!")
    else:
        print("部分测试失败!")
        exit(1)
    
    print("\n工具已准备就绪，可以处理文件。")
